# 浮光壁垒 UniApp 前端项目

## 项目简介

浮光壁垒是一个基于UniApp开发的跨平台移动应用，主要功能包括任务发布与接取、用户管理、消息通知等。项目采用Vue2 + uView UI框架开发，支持微信小程序、H5、App等多端运行。

## 技术栈

- **框架**: UniApp (Vue2)
- **UI库**: uView UI
- **状态管理**: Vuex
- **网络请求**: 封装的request工具
- **开发工具**: HBuilderX

## 项目结构

```
fuguang-uniapp/
├── api/                    # API接口封装
│   ├── auth.js            # 认证相关接口
│   ├── home.js            # 首页相关接口
│   └── task.js            # 任务相关接口
├── components/            # 公共组件
│   └── TaskCard/          # 任务卡片组件
├── config/                # 配置文件
│   └── index.js          # 环境配置
├── pages/                 # 页面文件
│   ├── index/            # 首页
│   ├── login/            # 登录页
│   ├── register/         # 注册页
│   ├── task/             # 任务相关页面
│   ├── user/             # 用户相关页面
│   ├── notice/           # 通知页面
│   └── agreement/        # 协议页面
├── static/               # 静态资源
├── store/                # Vuex状态管理
├── utils/                # 工具函数
│   ├── request.js        # 网络请求封装
│   └── common.js         # 通用工具函数
├── App.vue               # 应用入口
├── main.js               # 主入口文件
├── manifest.json         # 应用配置
├── pages.json            # 页面配置
└── uni.scss              # 全局样式
```

## 主要功能

### 1. 用户认证
- 用户注册/登录
- 实名认证
- 密码修改
- 头像上传

### 2. 首页功能
- 首页标语展示
- 位置定位
- 搜索功能
- 二维码扫描
- 系统通知滚动
- 兴业助农板块
- 购物专区
- 热门任务展示

### 3. 任务管理
- 任务列表浏览
- 任务详情查看
- 任务发布
- 任务接取
- 任务完成
- 我的任务管理

### 4. 用户中心
- 个人信息管理
- 实名认证
- 消息通知
- 设置功能

### 5. 通知系统
- 系统通知
- 活动通知
- 任务通知
- 未读消息提醒

## 开发环境搭建

### 1. 安装依赖
```bash
npm install
```

### 2. 运行项目

#### 微信小程序
```bash
npm run dev:mp-weixin
```

#### H5
```bash
npm run dev:h5
```

#### App
```bash
npm run dev:app-plus
```

### 3. 构建项目

#### 微信小程序
```bash
npm run build:mp-weixin
```

#### H5
```bash
npm run build:h5
```

#### App
```bash
npm run build:app-plus
```

## 配置说明

### 1. 环境配置
在 `config/index.js` 中配置不同环境的API地址：

```javascript
const config = {
  development: {
    baseURL: 'http://localhost:8888',
    timeout: 10000,
    debug: true
  },
  production: {
    baseURL: 'https://api.fuguang.com',
    timeout: 10000,
    debug: false
  }
}
```

### 2. 页面配置
在 `pages.json` 中配置页面路由和tabBar：

```json
{
  "pages": [...],
  "tabBar": {
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页"
      }
    ]
  }
}
```

## API接口

### 1. 认证接口
- `POST /app/login` - 用户登录
- `POST /app/register` - 用户注册
- `GET /app/user/profile` - 获取用户信息
- `PUT /app/user/profile` - 更新用户信息

### 2. 首页接口
- `GET /app/home/<USER>
- `GET /app/home/<USER>
- `GET /app/home/<USER>

### 3. 任务接口
- `GET /app/task/list` - 获取任务列表
- `GET /app/task/{id}` - 获取任务详情
- `POST /app/task` - 发布任务
- `POST /app/task/accept/{id}` - 接取任务

## 状态管理

使用Vuex管理全局状态：

```javascript
// 用户信息
this.$store.state.userInfo
this.$store.getters.isLoggedIn

// 位置信息
this.$store.state.location
this.$store.getters.currentLocation

// 未读消息
this.$store.state.unreadCount
this.$store.getters.hasUnreadMessage
```

## 工具函数

### 1. 网络请求
```javascript
import { get, post } from '@/utils/request'

// GET请求
const data = await get('/api/endpoint')

// POST请求
const result = await post('/api/endpoint', { data })
```

### 2. 通用工具
```javascript
import { formatTime, formatMoney, getCurrentLocation } from '@/utils/common'

// 格式化时间
const timeStr = formatTime(new Date())

// 格式化金额
const moneyStr = formatMoney(100.5)

// 获取当前位置
const location = await getCurrentLocation()
```

## 注意事项

1. **权限配置**: 确保在manifest.json中配置了必要的权限（位置、相机、相册等）
2. **网络安全**: 生产环境需要配置HTTPS
3. **小程序审核**: 发布微信小程序需要通过审核
4. **兼容性**: 注意不同平台的API差异

## 部署说明

### 1. 微信小程序
1. 使用微信开发者工具打开dist/dev/mp-weixin目录
2. 配置AppID
3. 上传代码并提交审核

### 2. H5部署
1. 构建H5版本：`npm run build:h5`
2. 将dist/build/h5目录部署到Web服务器

### 3. App打包
1. 使用HBuilderX云打包
2. 配置证书和包名
3. 生成安装包

## 开发规范

1. **代码风格**: 使用ESLint和Prettier保持代码风格一致
2. **组件命名**: 使用PascalCase命名组件
3. **文件命名**: 使用kebab-case命名文件
4. **提交规范**: 使用Conventional Commits规范

## 常见问题

### 1. 网络请求失败
- 检查API地址配置
- 确认网络连接
- 查看控制台错误信息

### 2. 页面跳转异常
- 检查pages.json配置
- 确认路由路径正确
- 查看页面是否存在

### 3. 组件样式问题
- 检查CSS作用域
- 确认uView组件使用正确
- 查看样式优先级

## 更新日志

### v1.0.0 (2025-01-18)
- 初始版本发布
- 完成基础功能开发
- 支持多端运行

## 联系方式

- 开发团队：浮光壁垒开发组
- 邮箱：<EMAIL>
- 技术支持：400-888-8888
