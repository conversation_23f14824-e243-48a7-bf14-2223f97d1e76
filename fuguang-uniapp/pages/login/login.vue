<template>
  <view class="login-container">
    <view class="header">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="title">浮光壁垒</text>
      <text class="subtitle">链接你我，共创未来</text>
    </view>
    
    <view class="form-container">
      <view class="input-group">
        <u-input 
          v-model="form.username" 
          placeholder="请输入用户名/手机号"
          prefix-icon="account"
          :clearable="true"
        />
      </view>
      
      <view class="input-group">
        <u-input 
          v-model="form.password" 
          type="password"
          placeholder="请输入密码"
          prefix-icon="lock"
          :clearable="true"
        />
      </view>
      
      <view class="agreement-row">
        <u-checkbox v-model="agreed" :size="28">
          我已阅读并同意
        </u-checkbox>
        <text class="link" @click="showAgreement('user')">《用户协议》</text>
        <text>和</text>
        <text class="link" @click="showAgreement('privacy')">《隐私政策》</text>
      </view>
      
      <u-button 
        type="primary" 
        :loading="loading"
        :disabled="!canLogin"
        @click="handleLogin"
        class="login-btn"
      >
        登录
      </u-button>
      
      <view class="footer-links">
        <text class="link" @click="guestMode">游客模式</text>
        <text class="link" @click="goRegister">立即注册</text>
      </view>
    </view>
  </view>
</template>

<script>
import { login } from '@/api/auth'

export default {
  data() {
    return {
      form: {
        username: '',
        password: ''
      },
      agreed: false,
      loading: false
    }
  },
  
  computed: {
    canLogin() {
      return this.form.username && this.form.password && this.agreed && !this.loading
    }
  },
  
  methods: {
    async handleLogin() {
      if (!this.canLogin) return
      
      this.loading = true
      try {
        const res = await login(this.form)
        
        // 保存token和用户信息
        uni.setStorageSync('token', res.token)
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        // 跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index'
          })
        }, 1500)
        
      } catch (error) {
        console.error('登录失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    goRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    },
    
    guestMode() {
      uni.showModal({
        title: '游客模式',
        content: '游客模式下功能受限，建议注册登录获得完整体验',
        confirmText: '继续',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 设置游客标识
            uni.setStorageSync('isGuest', true)
            uni.switchTab({
              url: '/pages/index/index'
            })
          }
        }
      })
    },
    
    showAgreement(type) {
      const url = type === 'user' ? '/pages/agreement/user' : '/pages/agreement/privacy'
      uni.navigateTo({ url })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
  
  .logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 20rpx;
  }
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.form-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 40rpx;
}

.agreement-row {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  font-size: 26rpx;
  color: #666;
  
  .link {
    color: #3cc51f;
    margin: 0 8rpx;
  }
}

.login-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.footer-links {
  display: flex;
  justify-content: space-between;
  
  .link {
    color: #3cc51f;
    font-size: 28rpx;
  }
}
</style>
