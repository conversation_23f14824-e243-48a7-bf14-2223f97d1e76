<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="bg-circle circle-1"></view>
      <view class="bg-circle circle-2"></view>
      <view class="bg-circle circle-3"></view>
    </view>

    <!-- 顶部装饰波浪 -->
    <view class="wave-decoration">
      <view class="wave wave-1"></view>
      <view class="wave wave-2"></view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-wrapper">
      <!-- 头部信息 -->
      <view class="header">
        <view class="logo-container">
          <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
          <view class="logo-glow"></view>
        </view>
        <text class="title">浮光壁垒</text>
        <text class="subtitle">链接你我，共创未来</text>
        <view class="title-underline"></view>
      </view>

      <!-- 登录表单 -->
      <view class="form-container">
        <view class="form-header">
          <text class="form-title">欢迎回来</text>
          <text class="form-desc">请登录您的账户</text>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <u-icon name="account" size="20" color="#666"></u-icon>
            </view>
            <u-input
              v-model="form.username"
              placeholder="请输入用户名/手机号"
              :border="false"
              :clearable="true"
              class="custom-input"
            />
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <u-icon name="lock" size="20" color="#666"></u-icon>
            </view>
            <u-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              :border="false"
              :clearable="true"
              class="custom-input"
            />
          </view>
        </view>

        <!-- 忘记密码 -->
        <view class="forgot-password">
          <text class="link" @click="forgotPassword">忘记密码？</text>
        </view>

        <!-- 协议同意 -->
        <view class="agreement-row">
          <u-checkbox v-model="agreed" :size="20" active-color="#667eea">
            <text class="agreement-text">我已阅读并同意</text>
          </u-checkbox>
          <text class="link" @click="showAgreement('user')">《用户协议》</text>
          <text class="agreement-text">和</text>
          <text class="link" @click="showAgreement('privacy')">《隐私政策》</text>
        </view>

        <!-- 登录按钮 -->
        <view class="btn-group">
          <u-button
            :loading="loading"
            :disabled="!canLogin"
            @click="handleLogin"
            class="login-btn"
            :custom-style="loginBtnStyle"
          >
            <text class="btn-text">登录</text>
          </u-button>
        </view>

        <!-- 分割线 -->
        <view class="divider">
          <view class="divider-line"></view>
          <text class="divider-text">或</text>
          <view class="divider-line"></view>
        </view>

        <!-- 其他登录方式 -->
        <view class="other-login">
          <view class="other-login-item" @click="wechatLogin">
            <view class="other-icon wechat-icon">
              <u-icon name="weixin" size="24" color="#07c160"></u-icon>
            </view>
            <text class="other-text">微信登录</text>
          </view>
          <view class="other-login-item" @click="guestMode">
            <view class="other-icon guest-icon">
              <u-icon name="account" size="24" color="#999"></u-icon>
            </view>
            <text class="other-text">游客模式</text>
          </view>
        </view>

        <!-- 底部链接 -->
        <view class="footer-links">
          <text class="footer-text">还没有账户？</text>
          <text class="link register-link" @click="goRegister">立即注册</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { login } from '@/api/auth'

export default {
  data() {
    return {
      form: {
        username: '',
        password: ''
      },
      agreed: false,
      loading: false
    }
  },
  
  computed: {
    canLogin() {
      return this.form.username && this.form.password && this.agreed && !this.loading
    },

    loginBtnStyle() {
      return {
        background: this.canLogin
          ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
          : 'linear-gradient(135deg, #ccc 0%, #999 100%)',
        border: 'none',
        borderRadius: '25px',
        height: '50px',
        boxShadow: this.canLogin ? '0 8px 20px rgba(102, 126, 234, 0.3)' : 'none'
      }
    }
  },
  
  methods: {
    async handleLogin() {
      if (!this.canLogin) return
      
      this.loading = true
      try {
        const res = await login(this.form)
        
        // 保存token和用户信息
        uni.setStorageSync('token', res.token)
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        // 跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index'
          })
        }, 1500)
        
      } catch (error) {
        console.error('登录失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    goRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    },
    
    guestMode() {
      uni.showModal({
        title: '游客模式',
        content: '游客模式下功能受限，建议注册登录获得完整体验',
        confirmText: '继续',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 设置游客标识
            uni.setStorageSync('isGuest', true)
            uni.switchTab({
              url: '/pages/index/index'
            })
          }
        }
      })
    },
    
    showAgreement(type) {
      const url = type === 'user' ? '/pages/agreement/user' : '/pages/agreement/privacy'
      uni.navigateTo({ url })
    },

    forgotPassword() {
      uni.showModal({
        title: '忘记密码',
        content: '请联系客服或通过注册手机号找回密码',
        showCancel: false,
        confirmText: '知道了'
      })
    },

    wechatLogin() {
      uni.showToast({
        title: '微信登录功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

// 背景装饰
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;

  .bg-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);

    &.circle-1 {
      width: 200rpx;
      height: 200rpx;
      top: 10%;
      right: -50rpx;
      animation: float 6s ease-in-out infinite;
    }

    &.circle-2 {
      width: 150rpx;
      height: 150rpx;
      top: 60%;
      left: -30rpx;
      animation: float 8s ease-in-out infinite reverse;
    }

    &.circle-3 {
      width: 100rpx;
      height: 100rpx;
      top: 30%;
      left: 20%;
      animation: float 10s ease-in-out infinite;
    }
  }
}

// 波浪装饰
.wave-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  z-index: 2;

  .wave {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);

    &.wave-1 {
      clip-path: polygon(0 0, 100% 0, 100% 60%, 0 80%);
      animation: wave 8s ease-in-out infinite;
    }

    &.wave-2 {
      clip-path: polygon(0 0, 100% 0, 100% 40%, 0 60%);
      animation: wave 10s ease-in-out infinite reverse;
      opacity: 0.5;
    }
  }
}

// 主要内容
.content-wrapper {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  padding: 80rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

// 头部
.header {
  text-align: center;
  margin-bottom: 80rpx;

  .logo-container {
    position: relative;
    display: inline-block;
    margin-bottom: 30rpx;

    .logo {
      width: 140rpx;
      height: 140rpx;
      border-radius: 50%;
      box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
    }

    .logo-glow {
      position: absolute;
      top: -10rpx;
      left: -10rpx;
      right: -10rpx;
      bottom: -10rpx;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
      animation: glow 3s ease-in-out infinite alternate;
    }
  }

  .title {
    display: block;
    font-size: 52rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 15rpx;
    text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
  }

  .subtitle {
    display: block;
    font-size: 30rpx;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20rpx;
  }

  .title-underline {
    width: 80rpx;
    height: 4rpx;
    background: linear-gradient(90deg, transparent, #ffffff, transparent);
    margin: 0 auto;
    border-radius: 2rpx;
  }
}

// 表单容器
.form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.form-header {
  text-align: center;
  margin-bottom: 50rpx;

  .form-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .form-desc {
    display: block;
    font-size: 26rpx;
    color: #666;
  }
}

// 输入框组
.input-group {
  margin-bottom: 30rpx;

  .input-wrapper {
    position: relative;
    background: #f8f9fa;
    border-radius: 15rpx;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #667eea;
      box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
    }

    .input-icon {
      position: absolute;
      left: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      z-index: 2;
    }

    .custom-input {
      padding-left: 60rpx !important;
      height: 90rpx;
      background: transparent;

      /deep/ .u-input__content {
        height: 90rpx;

        .u-input__content__field-wrapper {
          height: 90rpx;

          .u-input__content__field-wrapper__field {
            height: 90rpx;
            line-height: 90rpx;
            font-size: 30rpx;
          }
        }
      }
    }
  }
}

// 忘记密码
.forgot-password {
  text-align: right;
  margin-bottom: 30rpx;

  .link {
    font-size: 26rpx;
    color: #667eea;
  }
}

// 协议同意
.agreement-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  font-size: 24rpx;
  flex-wrap: wrap;

  .agreement-text {
    color: #666;
    margin: 0 4rpx;
  }

  .link {
    color: #667eea;
    margin: 0 4rpx;
  }
}

// 按钮组
.btn-group {
  margin-bottom: 40rpx;

  .login-btn {
    width: 100%;
    height: 100rpx;
    border-radius: 50rpx;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
    position: relative;
    overflow: hidden;

    .btn-text {
      color: #ffffff;
      font-weight: bold;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:active::before {
      left: 100%;
    }
  }
}

// 分割线
.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;

  .divider-line {
    flex: 1;
    height: 1rpx;
    background: #e0e0e0;
  }

  .divider-text {
    margin: 0 20rpx;
    font-size: 24rpx;
    color: #999;
  }
}

// 其他登录方式
.other-login {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;

  .other-login-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;

    .other-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;
      transition: all 0.3s ease;

      &.wechat-icon {
        background: rgba(7, 193, 96, 0.1);
        border: 2rpx solid rgba(7, 193, 96, 0.2);
      }

      &.guest-icon {
        background: rgba(153, 153, 153, 0.1);
        border: 2rpx solid rgba(153, 153, 153, 0.2);
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .other-text {
      font-size: 24rpx;
      color: #666;
    }
  }
}

// 底部链接
.footer-links {
  text-align: center;

  .footer-text {
    font-size: 26rpx;
    color: #666;
    margin-right: 10rpx;
  }

  .register-link {
    font-size: 26rpx;
    color: #667eea;
    font-weight: bold;
  }
}

// 动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes wave {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(10px);
  }
}

@keyframes glow {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .content-wrapper {
    padding: 60rpx 30rpx 30rpx;
  }

  .form-container {
    padding: 50rpx 30rpx;
  }

  .header .title {
    font-size: 48rpx;
  }
}
</style>
